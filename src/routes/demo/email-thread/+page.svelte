<script lang="ts">
	import EmailThread from '$lib/components/conversation/EmailThread.svelte';
	import { onMount } from 'svelte';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import {
		TicketSolid,
		UserHeadsetOutline,
		BullhornOutline,
		ExclamationCircleOutline
	} from 'flowbite-svelte-icons';
	import { Badge } from 'flowbite-svelte';

	let loading = false;
	let threads: any[] = [];

	// Handle thread toggle events
	function handleThreadToggle(event: CustomEvent) {
		console.log('Thread toggled:', event.detail);
	}

	// Handle email click events
	function handleEmailClick(event: CustomEvent) {
		console.log('Email clicked:', event.detail);
		// Here you could open a detailed email view, mark as read, etc.
	}

	// Handle email action events (for non-composition actions)
	function handleEmailAction(event: CustomEvent) {
		const { action, email } = event.detail;
		console.log(`Email action "${action}" triggered for:`, email);

		// Here you would implement the actual action logic for non-composition actions
		switch (action) {
			case 'addTask':
				alert(`Create task from email: ${email.subject}`);
				break;
			case 'addToCadence':
				alert(`Add email to cadence: ${email.subject}`);
				break;
			case 'instantBooker':
				alert(`Open instant booker for email: ${email.subject}`);
				break;
			case 'more':
				alert('Show more actions menu');
				break;
			default:
				console.log('Unknown action:', action);
		}
	}

	// Handle email send events (for composition)
	function handleEmailSend(event: CustomEvent) {
		const { action, originalMessageId, content, subject, to, cc, bcc } = event.detail;
		console.log(`Email ${action} sent:`, {
			originalMessageId,
			subject,
			to,
			cc,
			bcc,
			content
		});

		// Here you would implement the actual email sending logic
		let alertMessage = `Email ${action} sent successfully!\n\nTo: ${to.join(', ')}\nSubject: ${subject}\nContent: ${content.substring(0, 50)}...`;
		if (cc && cc.length > 0) {
			alertMessage += `\nCC: ${cc.join(', ')}`;
		}
		if (bcc && bcc.length > 0) {
			alertMessage += `\nBCC: ${bcc.join(', ')}`;
		}
		alertMessage += '\n\nThis would normally send the email to your backend.';
		alert(alertMessage);
	}

	function getPlatformIcon(platform: string): string {
		const icons = {
			EMAIL: '/images/platform-gmail.png'
		};
		return icons[platform];
	}

	onMount(() => {
		// Component will use mock data by default
	});
</script>

<svelte:head>
	<title>Email Thread</title>
</svelte:head>

<div class="bg-gray-100 h-screen overflow-hidden">
	<!-- Main content -->
	<div class="h-full">
		<div class="flex h-full">
			<!-- Sidebar -->
			<div class="w-[80px] h-full">
				<div class="bg-gray-50 shadow-sm border border-gray-200 h-full"></div>
			</div>
			<!-- Customer List -->
			<div class="w-1/4 h-full">
				<div class="bg-gray-50 shadow-sm border border-gray-200 h-full">
					<h3 class="text-lg font-medium text-gray-400 p-6 h-[120px]">
						CPI
					</h3>
					<button
						class="chat-item relative w-full p-4 text-left border border-gray-200"
					>
						<div
							class="flex items-start justify-between"
						>
							<!-- Avatar with Platform Icon Overlay -->
							<div
								class="mr-3 flex-shrink-0"
							>
								<div
									class="relative"
								>
									<!-- Main Avatar -->
									<div
										class="h-12 w-12 overflow-hidden rounded-full bg-gray-100"
									>
										<!-- Fallback initials when no picture_url or image failed to load -->
										<div
											class="flex h-full w-full items-center justify-center rounded-full bg-gray-100 font-medium text-gray-600"
										>
											{getInitials('Sarah Mitchell')}
										</div>
									</div>
									<!-- Platform Icon Overlay -->
									<div
										class="absolute -bottom-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full border border-white bg-white shadow-sm"
									>
										<img src={getPlatformIcon('EMAIL')} class="h-3 w-5" alt="Email" />
									</div>
								</div>
							</div>
							<div
								class="min-w-0 flex-1 pr-2"
							>
								<!-- Customer and Platform Info -->
								<div
									class="mb-1 flex items-center gap-2"
								>
									<span
										class="truncate font-medium text-gray-900"
									>
										Sarah Mitchell
									</span>
								</div>

								<!-- Latest Message Preview -->
								<div
									class="mt-1 truncate text-sm text-gray-600"
								>
									Email Message
								</div>
							</div>

							<!-- Right Side Info -->
							<div
								class="ml-2 flex flex-col items-end"
							>
								<!-- Time -->
								<span
									class="whitespace-nowrap text-xs text-gray-600"
								>
									Today
								</span>

								<!-- Unread Count -->
								<span
									class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
											bg-red-500 px-1.5 text-xs font-bold text-white"
								>
									1
								</span>
							</div>
						</div>
						<div
							class="mt-4 flex-row items-center gap-1 space-y-1 text-xs text-gray-700"
						>
							<!-- Ticket Badge -->
							<Badge border color='dark'>
								<TicketSolid class="mr-1 h-4 w-4 text-gray-600" />
								123
							</Badge>
							
							<!-- Priority Badge -->
							<Badge border color='dark'>
								<ExclamationCircleOutline class="mr-1 h-4 w-4 text-gray-600" />
								High
							</Badge>
							
							<!-- Owner Badge -->
							<Badge border color='dark'>
								<UserHeadsetOutline class="mr-1 h-4 w-4 text-gray-600" />
								Marketing
							</Badge>

							<!-- Channel Badge -->
							<Badge border color='dark'>
								<BullhornOutline class="mr-1 h-4 w-4 text-gray-600" />
								Email
							</Badge>
						</div>
					</button>
				</div>
			</div>

			<!-- Email Thread Component -->
			<div class="flex-1 h-full">
				<div class="bg-gray-50 shadow-sm border border-gray-200 flex flex-col h-full">
					<div class="px-6 py-6 border-b border-gray-200 flex-shrink-0 h-[120px]">
						<h2 class="text-lg font-medium text-gray-400">Header</h2>
					</div>

					<div class="bg-gray-50 flex-1 overflow-hidden">
						<EmailThread
							{threads}
							{loading}
							on:threadToggle={handleThreadToggle}
							on:emailClick={handleEmailClick}
							on:emailAction={handleEmailAction}
							on:emailSend={handleEmailSend}
						/>
					</div>
				</div>
			</div>

			<!-- Info Panel -->
			<div class="w-1/4 h-full">
				<div class="bg-gray-50 shadow-sm border border-gray-200 p-6 h-full">
					<h3 class="text-lg font-medium text-gray-400">
						Customer Info
					</h3>
				</div>
			</div>
		</div>
	</div>
</div>


